import logger from '../logger.js';
import { sleep } from '../utils/helpers.js';

/**
 * Generic provider handler for BuzzHeavier, MegaUp and other providers
 */
export class GenericHandler {
    constructor(scraper) {
        this.scraper = scraper;
        this.page = scraper.page;
    }

    /**
     * Process BuzzHeavier download link
     * @param {string} url - BuzzHeavier URL
     */
    async processBuzzHeavierLink(url) {
        try {
            logger.info(`Processing BuzzHeavier link: ${url}`);
            
            await this.scraper.navigateToUrl(url);
            await sleep(3000);
            
            // Look for download button or continue button
            const buttonSelectors = [
                'button:contains("Download")',
                'a:contains("Download")',
                'button:contains("Continue")',
                '.download-btn',
                '#download-button',
                '.btn-primary'
            ];
            
            return await this.processGenericProvider(buttonSelectors, 'BuzzHeavier');
            
        } catch (error) {
            logger.error(`Failed to process BuzzHeavier link: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process MegaUp download link
     * @param {string} url - MegaUp URL
     */
    async processMegaUpLink(url) {
        try {
            logger.info(`Processing MegaUp link: ${url}`);
            
            await this.scraper.navigateToUrl(url);
            await sleep(3000);
            
            // Look for download button
            const buttonSelectors = [
                '.download-button',
                '#download-btn',
                'a[href*="download"]',
                'button:contains("Download")',
                '.btn-download'
            ];
            
            return await this.processGenericProvider(buttonSelectors, 'MegaUp');
            
        } catch (error) {
            logger.error(`Failed to process MegaUp link: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process generic download link
     * @param {string} url - Generic URL
     */
    async processGenericLink(url) {
        try {
            logger.info(`Processing generic link: ${url}`);
            
            await this.scraper.navigateToUrl(url);
            await sleep(3000);
            
            // Generic selectors for download links
            const buttonSelectors = [
                'a[href*="download"]',
                'button:contains("Download")',
                'a:contains("Download")',
                '.download-link',
                '.download-button',
                '#download',
                '.btn-download',
                'input[value*="Download"]'
            ];
            
            return await this.processGenericProvider(buttonSelectors, 'Generic');
            
        } catch (error) {
            logger.error(`Failed to process generic link: ${error.message}`);
            throw error;
        }
    }

    /**
     * Generic provider processing logic
     * @param {Array} buttonSelectors - Array of button selectors to try
     * @param {string} providerName - Name of the provider for logging
     */
    async processGenericProvider(buttonSelectors, providerName) {
        try {
            // First, try to find and click download/continue buttons
            let buttonClicked = false;
            
            for (const selector of buttonSelectors) {
                try {
                    if (selector.includes(':contains(')) {
                        const text = selector.match(/contains\("([^"]+)"\)/)[1];
                        const xpath = `//button[contains(text(), "${text}")] | //a[contains(text(), "${text}")] | //input[@value[contains(., "${text}")]]`;
                        const elements = await this.page.$x(xpath);
                        
                        if (elements.length > 0) {
                            await elements[0].click();
                            logger.info(`${providerName}: Clicked button with text: ${text}`);
                            buttonClicked = true;
                            break;
                        }
                    } else {
                        const element = await this.page.$(selector);
                        if (element) {
                            await element.click();
                            logger.info(`${providerName}: Clicked button with selector: ${selector}`);
                            buttonClicked = true;
                            break;
                        }
                    }
                } catch (error) {
                    logger.debug(`${providerName}: Selector ${selector} not found`);
                }
            }
            
            if (buttonClicked) {
                // Wait for potential redirect
                await sleep(5000);
            }
            
            // Look for final download links
            const downloadLinkSelectors = [
                'a[href*=".zip"]',
                'a[href*=".rar"]',
                'a[href*=".7z"]',
                'a[href*=".exe"]',
                'a[download]',
                'a[href*="download"]',
                '.direct-download',
                '#direct-download'
            ];
            
            for (const selector of downloadLinkSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    for (const element of elements) {
                        const href = await this.page.evaluate(el => el.href, element);
                        if (href && href.includes('http') && !href.includes('javascript:')) {
                            logger.info(`${providerName}: Found download link: ${href}`);
                            return href;
                        }
                    }
                } catch (error) {
                    logger.debug(`${providerName}: Download selector ${selector} not found`);
                }
            }
            
            // If no direct download link found, return current URL
            const currentUrl = this.scraper.getCurrentUrl();
            logger.info(`${providerName}: Final URL: ${currentUrl}`);
            return currentUrl;
            
        } catch (error) {
            logger.error(`${providerName}: Failed to process: ${error.message}`);
            throw error;
        }
    }

    /**
     * Handle popup windows and fake redirects
     */
    async handlePopupsAndRedirects() {
        try {
            // Close any popup windows
            const pages = await this.scraper.browser.pages();
            if (pages.length > 1) {
                for (let i = 1; i < pages.length; i++) {
                    await pages[i].close();
                    logger.info('Closed popup window');
                }
            }
            
            // Check for fake download buttons (common anti-bot measure)
            const fakeButtonSelectors = [
                'a[href*="ads"]',
                'a[href*="popup"]',
                'a[href*="redirect"]',
                '.fake-download',
                '.ad-download'
            ];
            
            for (const selector of fakeButtonSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    if (elements.length > 0) {
                        logger.warn(`Detected potential fake download buttons: ${selector}`);
                    }
                } catch (error) {
                    // Continue checking
                }
            }
            
        } catch (error) {
            logger.error(`Error handling popups and redirects: ${error.message}`);
        }
    }
}

export default GenericHandler;
