import puppeteer from 'puppeteer-extra';
import StealthPlugin from 'puppeteer-extra-plugin-stealth';
import AdblockerPlugin from 'puppeteer-extra-plugin-adblocker';
import logger from './logger.js';
import config from './utils/config.js';
import { sleep, retryWithBackoff, sanitizeFilename, extractDomain, isValidUrl } from './utils/helpers.js';
import DataNodesHandler from './providers/dataNodes.js';
import GoFileHandler from './providers/goFile.js';
import GenericHandler from './providers/generic.js';

// Configure puppeteer plugins
puppeteer.use(StealthPlugin());
puppeteer.use(AdblockerPlugin({ blockTrackers: true }));

class SteamripScraper {
    constructor() {
        this.browser = null;
        this.page = null;
        this.results = [];
        this.config = config;

        // Initialize provider handlers (will be set after page is created)
        this.dataNodesHandler = null;
        this.goFileHandler = null;
        this.genericHandler = null;
    }

    /**
     * Initialize the browser and page
     */
    async initialize() {
        try {
            logger.info('Initializing browser...');
            
            const browserConfig = this.config.getBrowserConfig();
            
            this.browser = await puppeteer.launch({
                headless: browserConfig.headless,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--disable-gpu',
                    '--disable-background-timer-throttling',
                    '--disable-backgrounding-occluded-windows',
                    '--disable-renderer-backgrounding'
                ],
                defaultViewport: browserConfig.viewport
            });

            this.page = await this.browser.newPage();
            
            // Set user agent
            await this.page.setUserAgent(browserConfig.userAgent);
            
            // Set extra headers
            await this.page.setExtraHTTPHeaders({
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            });

            // Block unnecessary resources to speed up loading
            await this.page.setRequestInterception(true);
            this.page.on('request', (req) => {
                const resourceType = req.resourceType();
                if (['image', 'stylesheet', 'font', 'media'].includes(resourceType)) {
                    req.abort();
                } else {
                    req.continue();
                }
            });

            // Handle popup windows
            this.browser.on('targetcreated', async (target) => {
                if (target.type() === 'page') {
                    const newPage = await target.page();
                    if (newPage) {
                        logger.info('Popup detected and will be closed');
                        await newPage.close();
                    }
                }
            });

            // Initialize provider handlers
            this.dataNodesHandler = new DataNodesHandler(this);
            this.goFileHandler = new GoFileHandler(this);
            this.genericHandler = new GenericHandler(this);

            logger.info('Browser initialized successfully');

        } catch (error) {
            logger.error(`Failed to initialize browser: ${error.message}`);
            throw error;
        }
    }

    /**
     * Navigate to a URL with retry logic
     * @param {string} url - URL to navigate to
     * @param {Object} options - Navigation options
     */
    async navigateToUrl(url, options = {}) {
        const defaultOptions = {
            waitUntil: 'networkidle2',
            timeout: 30000
        };
        
        const navOptions = { ...defaultOptions, ...options };
        
        return await retryWithBackoff(
            async () => {
                logger.info(`Navigating to: ${url}`);
                await this.page.goto(url, navOptions);
                await sleep(this.config.getDelays().pageLoad);
                logger.info(`Successfully navigated to: ${url}`);
            },
            this.config.getRetryConfig().maxRetries,
            this.config.getRetryConfig().retryDelay,
            `navigation to ${url}`
        );
    }

    /**
     * Wait for element with retry logic
     * @param {string} selector - CSS selector
     * @param {Object} options - Wait options
     */
    async waitForElement(selector, options = {}) {
        const defaultOptions = {
            timeout: 10000,
            visible: true
        };
        
        const waitOptions = { ...defaultOptions, ...options };
        
        return await retryWithBackoff(
            async () => {
                logger.debug(`Waiting for element: ${selector}`);
                const element = await this.page.waitForSelector(selector, waitOptions);
                logger.debug(`Element found: ${selector}`);
                return element;
            },
            2,
            1000,
            `waiting for element ${selector}`
        );
    }

    /**
     * Click element with retry logic
     * @param {string} selector - CSS selector
     * @param {Object} options - Click options
     */
    async clickElement(selector, options = {}) {
        return await retryWithBackoff(
            async () => {
                logger.debug(`Clicking element: ${selector}`);
                await this.page.click(selector, options);
                await sleep(1000); // Small delay after click
                logger.debug(`Successfully clicked: ${selector}`);
            },
            2,
            1000,
            `clicking element ${selector}`
        );
    }

    /**
     * Get current page URL
     */
    getCurrentUrl() {
        return this.page.url();
    }

    /**
     * Check if page contains specific text
     * @param {string} text - Text to search for
     */
    async pageContainsText(text) {
        try {
            const content = await this.page.content();
            return content.includes(text);
        } catch (error) {
            logger.warn(`Failed to check page content: ${error.message}`);
            return false;
        }
    }

    /**
     * Close browser and cleanup
     */
    async cleanup() {
        try {
            if (this.page) {
                await this.page.close();
            }
            if (this.browser) {
                await this.browser.close();
            }
            logger.info('Browser cleanup completed');
        } catch (error) {
            logger.error(`Error during cleanup: ${error.message}`);
        }
    }

    /**
     * Search for a game on steamrip.com
     * @param {string} gameName - Name of the game to search for
     */
    async searchGame(gameName) {
        try {
            logger.info(`Searching for game: ${gameName}`);

            // Construct search URL
            const searchUrl = this.config.getSearchUrlTemplate().replace('{search_query}', encodeURIComponent(gameName));

            // Navigate to search page
            await this.navigateToUrl(searchUrl);

            // Wait for search results to load
            await this.waitForElement('.post', { timeout: 15000 });

            logger.info(`Search completed for: ${gameName}`);
            return true;

        } catch (error) {
            logger.error(`Failed to search for game ${gameName}: ${error.message}`);
            return false;
        }
    }

    /**
     * Get the first search result link
     */
    async getFirstSearchResult() {
        try {
            logger.info('Looking for first search result...');

            // Look for the first post/article link
            const selectors = [
                '.post h2 a',
                '.post-title a',
                'article h2 a',
                '.entry-title a',
                'h2.post-title a'
            ];

            let firstResultLink = null;

            for (const selector of selectors) {
                try {
                    const elements = await this.page.$$(selector);
                    if (elements.length > 0) {
                        firstResultLink = await this.page.evaluate(el => el.href, elements[0]);
                        logger.info(`Found first result using selector: ${selector}`);
                        break;
                    }
                } catch (error) {
                    logger.debug(`Selector ${selector} not found, trying next...`);
                }
            }

            if (!firstResultLink) {
                throw new Error('No search results found');
            }

            logger.info(`First search result URL: ${firstResultLink}`);
            return firstResultLink;

        } catch (error) {
            logger.error(`Failed to get first search result: ${error.message}`);
            throw error;
        }
    }

    /**
     * Navigate to the first search result
     */
    async navigateToFirstResult() {
        try {
            const firstResultUrl = await this.getFirstSearchResult();
            await this.navigateToUrl(firstResultUrl);

            // Wait for game page to load
            await this.waitForElement('body', { timeout: 15000 });

            logger.info('Successfully navigated to first search result');
            return firstResultUrl;

        } catch (error) {
            logger.error(`Failed to navigate to first result: ${error.message}`);
            throw error;
        }
    }

    /**
     * Process a single game search and navigation
     * @param {string} gameName - Name of the game to process
     */
    async processGame(gameName) {
        try {
            logger.info(`Starting to process game: ${gameName}`);

            // Search for the game
            const searchSuccess = await this.searchGame(gameName);
            if (!searchSuccess) {
                throw new Error(`Failed to search for game: ${gameName}`);
            }

            // Navigate to first result
            const gamePageUrl = await this.navigateToFirstResult();

            // Add delay between requests
            await sleep(this.config.getDelays().betweenRequests);

            logger.info(`Successfully processed game: ${gameName}`);
            return {
                gameName,
                gamePageUrl,
                success: true
            };

        } catch (error) {
            logger.error(`Failed to process game ${gameName}: ${error.message}`);
            return {
                gameName,
                gamePageUrl: null,
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Extract download links from the current game page
     */
    async extractDownloadLinks() {
        try {
            logger.info('Extracting download links from game page...');

            const downloadLinks = [];

            // Common selectors for download links on steamrip
            const linkSelectors = [
                'a[href*="gofile"]',
                'a[href*="buzzheavier"]',
                'a[href*="megaup"]',
                'a[href*="datanodes"]',
                'a[href*="filecrypt"]',
                'a[href*="download"]',
                '.download-link a',
                '.download-button a',
                '.btn-download',
                'a[class*="download"]'
            ];

            // Extract all potential download links
            for (const selector of linkSelectors) {
                try {
                    const elements = await this.page.$$(selector);

                    for (const element of elements) {
                        const href = await this.page.evaluate(el => el.href, element);
                        const text = await this.page.evaluate(el => el.textContent.trim(), element);

                        if (href && isValidUrl(href)) {
                            const provider = this.identifyProvider(href);

                            downloadLinks.push({
                                url: href,
                                text: text,
                                provider: provider,
                                domain: extractDomain(href)
                            });
                        }
                    }
                } catch (error) {
                    logger.debug(`Error with selector ${selector}: ${error.message}`);
                }
            }

            // Remove duplicates
            const uniqueLinks = downloadLinks.filter((link, index, self) =>
                index === self.findIndex(l => l.url === link.url)
            );

            logger.info(`Found ${uniqueLinks.length} unique download links`);

            return uniqueLinks;

        } catch (error) {
            logger.error(`Failed to extract download links: ${error.message}`);
            return [];
        }
    }

    /**
     * Identify download provider from URL
     * @param {string} url - Download URL
     */
    identifyProvider(url) {
        const domain = extractDomain(url).toLowerCase();

        if (domain.includes('gofile')) return 'GoFile';
        if (domain.includes('buzzheavier')) return 'BuzzHeavier';
        if (domain.includes('megaup')) return 'MegaUp';
        if (domain.includes('datanodes')) return 'DataNodes';
        if (domain.includes('filecrypt')) return 'FileCrypt';
        if (domain.includes('mediafire')) return 'MediaFire';
        if (domain.includes('mega.nz')) return 'Mega';
        if (domain.includes('1fichier')) return '1Fichier';
        if (domain.includes('rapidgator')) return 'RapidGator';
        if (domain.includes('turbobit')) return 'TurboBit';

        return 'Unknown';
    }

    /**
     * Process download links and extract final URLs
     * @param {Array} downloadLinks - Array of download link objects
     */
    async processDownloadLinks(downloadLinks) {
        const processedLinks = [];

        for (const link of downloadLinks) {
            try {
                logger.info(`Processing ${link.provider} link: ${link.url}`);

                let finalUrl = null;

                switch (link.provider) {
                    case 'DataNodes':
                        finalUrl = await this.dataNodesHandler.processLink(link.url);
                        break;
                    case 'GoFile':
                        finalUrl = await this.goFileHandler.processLink(link.url);
                        break;
                    case 'BuzzHeavier':
                        finalUrl = await this.genericHandler.processBuzzHeavierLink(link.url);
                        break;
                    case 'MegaUp':
                        finalUrl = await this.genericHandler.processMegaUpLink(link.url);
                        break;
                    default:
                        finalUrl = await this.genericHandler.processGenericLink(link.url);
                        break;
                }

                processedLinks.push({
                    ...link,
                    finalUrl: finalUrl,
                    processed: finalUrl !== null
                });

                // Add delay between processing links
                await sleep(this.config.getDelays().betweenRequests);

            } catch (error) {
                logger.error(`Failed to process ${link.provider} link: ${error.message}`);
                processedLinks.push({
                    ...link,
                    finalUrl: null,
                    processed: false,
                    error: error.message
                });
            }
        }

        return processedLinks;
    }

    /**
     * Detect and handle captchas on current page
     */
    async detectAndHandleCaptcha() {
        try {
            logger.info('Checking for captchas...');

            const captchaSelectors = [
                '.captcha',
                '#captcha',
                '.g-recaptcha',
                '.h-captcha',
                'iframe[src*="captcha"]',
                'iframe[src*="recaptcha"]',
                '[data-sitekey]',
                '.cf-turnstile'
            ];

            let captchaDetected = false;
            let captchaType = 'unknown';

            for (const selector of captchaSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        captchaDetected = true;
                        if (selector.includes('recaptcha')) captchaType = 'reCAPTCHA';
                        else if (selector.includes('h-captcha')) captchaType = 'hCaptcha';
                        else if (selector.includes('turnstile')) captchaType = 'Cloudflare Turnstile';
                        else captchaType = 'Generic Captcha';

                        logger.warn(`${captchaType} detected with selector: ${selector}`);
                        break;
                    }
                } catch (error) {
                    // Continue checking other selectors
                }
            }

            if (captchaDetected) {
                logger.warn(`Captcha detected: ${captchaType}. Waiting for manual solving...`);

                // Wait for captcha to be solved (manual intervention required)
                await sleep(this.config.getDelays().captchaSolve);

                // Check if captcha was solved by looking for submit buttons or form changes
                const submitSelectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    '.submit-btn',
                    '#submit',
                    'button:contains("Submit")',
                    'button:contains("Continue")'
                ];

                for (const selector of submitSelectors) {
                    try {
                        if (selector.includes(':contains(')) {
                            const text = selector.match(/contains\("([^"]+)"\)/)[1];
                            const xpath = `//button[contains(text(), "${text}")]`;
                            const elements = await this.page.$x(xpath);
                            if (elements.length > 0) {
                                await elements[0].click();
                                logger.info(`Clicked submit button after captcha: ${text}`);
                                await sleep(3000);
                                return true;
                            }
                        } else {
                            const element = await this.page.$(selector);
                            if (element) {
                                await element.click();
                                logger.info(`Clicked submit button after captcha: ${selector}`);
                                await sleep(3000);
                                return true;
                            }
                        }
                    } catch (error) {
                        logger.debug(`Submit selector ${selector} not found`);
                    }
                }
            }

            return captchaDetected;

        } catch (error) {
            logger.error(`Error detecting captcha: ${error.message}`);
            return false;
        }
    }

    /**
     * Handle popup windows and fake redirects
     */
    async handlePopupsAndRedirects() {
        try {
            // Close popup windows
            const pages = await this.browser.pages();
            if (pages.length > 1) {
                for (let i = 1; i < pages.length; i++) {
                    try {
                        await pages[i].close();
                        logger.info('Closed popup window');
                    } catch (error) {
                        logger.debug(`Failed to close popup: ${error.message}`);
                    }
                }
            }

            // Detect fake download buttons
            const fakeButtonSelectors = [
                'a[href*="ads"]',
                'a[href*="popup"]',
                'a[href*="redirect"]',
                'a[href*="affiliate"]',
                '.fake-download',
                '.ad-download',
                '[onclick*="popup"]',
                '[onclick*="window.open"]'
            ];

            let fakeButtonsFound = 0;
            for (const selector of fakeButtonSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    if (elements.length > 0) {
                        fakeButtonsFound += elements.length;
                        logger.warn(`Detected ${elements.length} potential fake buttons: ${selector}`);
                    }
                } catch (error) {
                    // Continue checking
                }
            }

            if (fakeButtonsFound > 0) {
                logger.warn(`Total fake buttons detected: ${fakeButtonsFound}`);
            }

            // Check for redirect attempts
            const currentUrl = this.getCurrentUrl();
            if (currentUrl.includes('redirect') || currentUrl.includes('ads') || currentUrl.includes('popup')) {
                logger.warn(`Potential redirect URL detected: ${currentUrl}`);
                return false;
            }

            return true;

        } catch (error) {
            logger.error(`Error handling popups and redirects: ${error.message}`);
            return false;
        }
    }

    /**
     * Main orchestration method to scrape all games
     * @param {Array} gameList - Optional custom game list
     */
    async scrapeAllGames(gameList = null) {
        const games = gameList || this.config.getGames();
        const results = [];

        try {
            logger.info(`Starting to scrape ${games.length} games...`);

            for (let i = 0; i < games.length; i++) {
                const gameName = games[i];
                logger.info(`Processing game ${i + 1}/${games.length}: ${gameName}`);

                try {
                    // Process the game
                    const gameResult = await this.processGame(gameName);

                    if (gameResult.success) {
                        // Extract download links
                        const downloadLinks = await this.extractDownloadLinks();

                        // Process download links
                        const processedLinks = await this.processDownloadLinks(downloadLinks);

                        // Handle anti-bot measures
                        await this.handlePopupsAndRedirects();

                        const result = {
                            ...gameResult,
                            downloadLinks: downloadLinks,
                            processedLinks: processedLinks,
                            timestamp: new Date().toISOString()
                        };

                        results.push(result);
                        this.results.push(result);

                        logger.info(`Successfully processed ${gameName}: ${processedLinks.length} links found`);
                    } else {
                        results.push(gameResult);
                        logger.error(`Failed to process ${gameName}: ${gameResult.error}`);
                    }

                } catch (error) {
                    logger.error(`Error processing game ${gameName}: ${error.message}`);
                    results.push({
                        gameName,
                        success: false,
                        error: error.message,
                        timestamp: new Date().toISOString()
                    });
                }

                // Add delay between games
                if (i < games.length - 1) {
                    await sleep(this.config.getDelays().betweenRequests);
                }
            }

            logger.info(`Completed scraping all games. Total results: ${results.length}`);
            return results;

        } catch (error) {
            logger.error(`Fatal error during scraping: ${error.message}`);
            throw error;
        }
    }

    /**
     * Save results to file
     * @param {string} filename - Output filename
     */
    async saveResults(filename = 'results.json') {
        try {
            const fs = await import('fs');
            const path = await import('path');

            const outputDir = 'output';
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }

            const outputPath = path.join(outputDir, filename);
            const data = {
                timestamp: new Date().toISOString(),
                totalGames: this.results.length,
                results: this.results
            };

            fs.writeFileSync(outputPath, JSON.stringify(data, null, 2));
            logger.info(`Results saved to: ${outputPath}`);

            return outputPath;

        } catch (error) {
            logger.error(`Failed to save results: ${error.message}`);
            throw error;
        }
    }

    /**
     * Get scraping results
     */
    getResults() {
        return this.results;
    }

    /**
     * Get summary statistics
     */
    getSummary() {
        const totalGames = this.results.length;
        const successfulGames = this.results.filter(r => r.success).length;
        const totalLinks = this.results.reduce((sum, r) => sum + (r.processedLinks?.length || 0), 0);
        const successfulLinks = this.results.reduce((sum, r) =>
            sum + (r.processedLinks?.filter(l => l.processed).length || 0), 0);

        return {
            totalGames,
            successfulGames,
            failedGames: totalGames - successfulGames,
            totalLinks,
            successfulLinks,
            failedLinks: totalLinks - successfulLinks,
            successRate: totalGames > 0 ? (successfulGames / totalGames * 100).toFixed(2) + '%' : '0%'
        };
    }
}

export default SteamripScraper;
