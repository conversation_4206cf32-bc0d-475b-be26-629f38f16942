import logger from '../logger.js';
import { sleep, retryWithBackoff } from '../utils/helpers.js';

/**
 * DataNodes provider handler
 */
export class DataNodesHandler {
    constructor(scraper) {
        this.scraper = scraper;
        this.page = scraper.page;
    }

    /**
     * Process DataNodes download link
     * @param {string} url - DataNodes URL
     */
    async processLink(url) {
        try {
            logger.info(`Processing DataNodes link: ${url}`);
            
            // Navigate to DataNodes page
            await this.scraper.navigateToUrl(url);
            
            // Wait for page to load
            await sleep(3000);
            
            // Look for "Continue to Download" button
            const continueButtonSelectors = [
                'button:contains("Continue to Download")',
                'a:contains("Continue to Download")',
                '.btn:contains("Continue")',
                '#continue-button',
                '.continue-btn',
                'input[value*="Continue"]',
                'button[onclick*="continue"]'
            ];
            
            let continueButton = null;
            
            for (const selector of continueButtonSelectors) {
                try {
                    // For text-based selectors, use XPath
                    if (selector.includes(':contains(')) {
                        const text = selector.match(/contains\("([^"]+)"\)/)[1];
                        const xpath = `//button[contains(text(), "${text}")] | //a[contains(text(), "${text}")] | //input[@value[contains(., "${text}")]]`;
                        
                        const elements = await this.page.$x(xpath);
                        if (elements.length > 0) {
                            continueButton = elements[0];
                            logger.info(`Found continue button with text: ${text}`);
                            break;
                        }
                    } else {
                        // Regular CSS selector
                        continueButton = await this.page.$(selector);
                        if (continueButton) {
                            logger.info(`Found continue button with selector: ${selector}`);
                            break;
                        }
                    }
                } catch (error) {
                    logger.debug(`Selector ${selector} not found: ${error.message}`);
                }
            }
            
            if (!continueButton) {
                throw new Error('Continue button not found on DataNodes page');
            }
            
            // Click the continue button
            await continueButton.click();
            logger.info('Clicked continue button');
            
            // Wait for redirect or new page
            await sleep(5000);
            
            // Check if we're on a download page or if there's a direct download link
            const currentUrl = this.scraper.getCurrentUrl();
            
            // Look for direct download links
            const downloadLinkSelectors = [
                'a[href*=".zip"]',
                'a[href*=".rar"]',
                'a[href*=".7z"]',
                'a[download]',
                '.download-link',
                '#download-link',
                'a:contains("Download")'
            ];
            
            for (const selector of downloadLinkSelectors) {
                try {
                    let downloadLink = null;
                    
                    if (selector.includes(':contains(')) {
                        const xpath = '//a[contains(text(), "Download")]';
                        const elements = await this.page.$x(xpath);
                        if (elements.length > 0) {
                            downloadLink = await this.page.evaluate(el => el.href, elements[0]);
                        }
                    } else {
                        const element = await this.page.$(selector);
                        if (element) {
                            downloadLink = await this.page.evaluate(el => el.href, element);
                        }
                    }
                    
                    if (downloadLink && downloadLink.includes('http')) {
                        logger.info(`Found DataNodes download link: ${downloadLink}`);
                        return downloadLink;
                    }
                } catch (error) {
                    logger.debug(`Error checking selector ${selector}: ${error.message}`);
                }
            }
            
            // If no direct download link found, return the current URL
            logger.info(`DataNodes final URL: ${currentUrl}`);
            return currentUrl;
            
        } catch (error) {
            logger.error(`Failed to process DataNodes link: ${error.message}`);
            throw error;
        }
    }
}

export default DataNodesHandler;
