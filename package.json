{"name": "steamrip-scraper", "version": "1.0.0", "description": "Automated web scraping tool for steamrip.com with download link extraction", "main": "src/index.js", "type": "module", "scripts": {"start": "node src/index.js", "dev": "node --inspect src/index.js", "test": "node test/test.js"}, "keywords": ["web-scraping", "automation", "puppeteer", "steamrip", "download-links"], "author": "Your Name", "license": "MIT", "dependencies": {"puppeteer": "^21.5.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-adblocker": "^2.13.6", "commander": "^11.1.0", "chalk": "^5.3.0", "ora": "^7.0.1", "winston": "^3.11.0", "dotenv": "^16.3.1"}, "devDependencies": {"eslint": "^8.55.0"}, "engines": {"node": ">=16.0.0"}}