# SteamRip Scraper - Usage Guide

## Quick Start

1. **Install dependencies:**
```bash
npm install
```

2. **Copy environment configuration:**
```bash
cp .env.example .env
```

3. **Run the scraper:**
```bash
npm start
```

## Command Line Options

### Basic Usage
```bash
# Run with default settings
npm start

# Run with specific games
npm start -- --games "Elden Ring,Cyberpunk 2077,FIFA 17"

# Run in headless mode (no browser window)
npm start -- --headless

# Custom output file
npm start -- --output my-results.json

# Verbose logging
npm start -- --verbose

# Custom configuration file
npm start -- --config my-config.json
```

### Advanced Usage
```bash
# Combine multiple options
npm start -- --games "Elden Ring" --headless --verbose --output elden-ring-results.json
```

## Configuration

### Game List Configuration (`config/games.json`)

Edit the `games` array to customize which games to search for:

```json
{
  "games": [
    "Elden Ring",
    "Cyberpunk 2077",
    "FIFA 17",
    "FIFA 16",
    "Your Custom Game"
  ],
  "settings": {
    // ... other settings
  }
}
```

### Environment Variables (`.env`)

Customize behavior by editing the `.env` file:

```env
# Browser Configuration
HEADLESS=false                    # Run browser in headless mode
VIEWPORT_WIDTH=1920              # Browser window width
VIEWPORT_HEIGHT=1080             # Browser window height

# Timing Configuration (milliseconds)
PAGE_LOAD_DELAY=3000            # Wait time after page loads
BETWEEN_REQUESTS_DELAY=2000     # Delay between requests
CAPTCHA_SOLVE_DELAY=10000       # Time to wait for captcha solving
DOWNLOAD_WAIT_DELAY=5000        # Wait time for downloads

# Retry Configuration
MAX_RETRIES=3                   # Maximum retry attempts
RETRY_DELAY=5000               # Base delay between retries

# Logging
LOG_LEVEL=info                 # Logging level (debug, info, warn, error)
LOG_FILE=logs/scraper.log      # Log file path

# Output
OUTPUT_DIR=output              # Output directory
RESULTS_FILE=results.json      # Default results filename
```

## Understanding the Output

### Results File Structure

The scraper generates a JSON file with the following structure:

```json
{
  "timestamp": "2024-01-01T12:00:00.000Z",
  "totalGames": 4,
  "results": [
    {
      "gameName": "Elden Ring",
      "gamePageUrl": "https://steamrip.com/elden-ring/",
      "success": true,
      "downloadLinks": [
        {
          "url": "https://gofile.io/d/abc123",
          "text": "GoFile Download",
          "provider": "GoFile",
          "domain": "gofile.io"
        }
      ],
      "processedLinks": [
        {
          "url": "https://gofile.io/d/abc123",
          "text": "GoFile Download",
          "provider": "GoFile",
          "domain": "gofile.io",
          "finalUrl": "https://store1.gofile.io/download/direct/abc123/game.zip",
          "processed": true
        }
      ],
      "timestamp": "2024-01-01T12:00:00.000Z"
    }
  ]
}
```

### Console Output

The scraper provides real-time progress information:

```
🎮 Games to process: 4
  1. Elden Ring
  2. Cyberpunk 2077
  3. FIFA 17
  4. FIFA 16

🚀 Starting scraping process...

✅ Elden Ring: 3 links found
❌ Cyberpunk 2077: No search results found
✅ FIFA 17: 2 links found
✅ FIFA 16: 4 links found

📊 Final Summary:
  Total games processed: 4
  Successful: 3
  Failed: 1
  Total download links: 9
  Success rate: 75.00%
```

## Supported Download Providers

The scraper can handle various download providers:

### Priority Providers (Advanced Handling)
- **DataNodes**: Automatically clicks "Continue to Download" buttons
- **GoFile**: Handles FileCrypt captchas and redirects

### Standard Providers
- **BuzzHeavier**: Basic redirect handling
- **MegaUp**: Basic redirect handling
- **MediaFire**: Generic link extraction
- **Mega.nz**: Generic link extraction
- **1Fichier**: Generic link extraction

### Generic Handling
Any other provider will use generic link extraction methods.

## Troubleshooting

### Common Issues

1. **Browser fails to start:**
   - Ensure you have sufficient system resources
   - Try running in headless mode: `npm start -- --headless`
   - Check if Chrome/Chromium is properly installed

2. **Captcha challenges:**
   - The scraper will pause for manual captcha solving
   - Keep the browser window open when captchas appear
   - Increase `CAPTCHA_SOLVE_DELAY` if needed

3. **No search results found:**
   - Check if the game name is spelled correctly
   - Try alternative game names or abbreviations
   - Verify steamrip.com is accessible

4. **Download links not working:**
   - Some providers may have temporary issues
   - Links may expire after a certain time
   - Try running the scraper again

### Debug Mode

Enable verbose logging for troubleshooting:

```bash
npm start -- --verbose
```

This will show detailed information about:
- Browser navigation steps
- Element detection attempts
- Provider-specific handling
- Error details

### Log Files

Check the log files for detailed information:
- `logs/scraper.log` - General operation logs
- `logs/error.log` - Error-specific logs

## Performance Tips

1. **Reduce delays for faster execution:**
   ```env
   PAGE_LOAD_DELAY=2000
   BETWEEN_REQUESTS_DELAY=1000
   ```

2. **Use headless mode for better performance:**
   ```bash
   npm start -- --headless
   ```

3. **Process fewer games at once:**
   ```bash
   npm start -- --games "Single Game"
   ```

4. **Monitor system resources:**
   - Close other browser instances
   - Ensure sufficient RAM and CPU

## Legal and Ethical Usage

⚠️ **Important Reminders:**

1. **Educational Purpose Only**: This tool is for learning web scraping techniques
2. **Respect Terms of Service**: Always comply with website terms of service
3. **Rate Limiting**: The scraper includes delays to avoid overwhelming servers
4. **Copyright Compliance**: Ensure your usage complies with copyright laws
5. **Responsible Usage**: Don't use for commercial purposes or mass downloading

## Getting Help

If you encounter issues:

1. Check this usage guide
2. Review the log files
3. Try running with `--verbose` flag
4. Check the GitHub issues page
5. Ensure you're using the latest version

Remember: This tool is for educational purposes only. Use responsibly and ethically!
