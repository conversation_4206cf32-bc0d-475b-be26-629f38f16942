import logger from '../logger.js';
import { sleep, retryWithBackoff } from '../utils/helpers.js';

/**
 * GoFile provider handler with FileCrypt captcha support
 */
export class GoFileHandler {
    constructor(scraper) {
        this.scraper = scraper;
        this.page = scraper.page;
    }

    /**
     * Process GoFile download link
     * @param {string} url - GoFile URL
     */
    async processLink(url) {
        try {
            logger.info(`Processing GoFile link: ${url}`);
            
            // Navigate to GoFile page
            await this.scraper.navigateToUrl(url);
            
            // Wait for page to load
            await sleep(3000);
            
            // Check if this is a FileCrypt page (common for GoFile links)
            if (await this.isFileCryptPage()) {
                return await this.handleFileCryptPage();
            }
            
            // Handle direct GoFile page
            return await this.handleGoFilePage();
            
        } catch (error) {
            logger.error(`Failed to process GoFile link: ${error.message}`);
            throw error;
        }
    }

    /**
     * Check if current page is FileCrypt
     */
    async isFileCryptPage() {
        try {
            const url = this.scraper.getCurrentUrl();
            const content = await this.page.content();
            
            return url.includes('filecrypt') || 
                   content.includes('filecrypt') || 
                   content.includes('captcha');
        } catch (error) {
            return false;
        }
    }

    /**
     * Handle FileCrypt page with captcha
     */
    async handleFileCryptPage() {
        try {
            logger.info('Detected FileCrypt page, handling captcha...');
            
            // Wait for captcha to load
            await sleep(5000);
            
            // Look for captcha elements
            const captchaSelectors = [
                '.captcha',
                '#captcha',
                'iframe[src*="captcha"]',
                'iframe[src*="recaptcha"]',
                '.g-recaptcha',
                '.h-captcha'
            ];
            
            let captchaFound = false;
            for (const selector of captchaSelectors) {
                try {
                    const element = await this.page.$(selector);
                    if (element) {
                        captchaFound = true;
                        logger.info(`Captcha detected with selector: ${selector}`);
                        break;
                    }
                } catch (error) {
                    // Continue checking other selectors
                }
            }
            
            if (captchaFound) {
                logger.warn('Captcha detected - manual intervention may be required');
                
                // Wait for potential manual captcha solving
                await sleep(this.scraper.config.getDelays().captchaSolve);
                
                // Try to find and click continue/submit button after captcha
                const submitSelectors = [
                    'button[type="submit"]',
                    'input[type="submit"]',
                    '.btn-submit',
                    '#submit',
                    'button:contains("Continue")',
                    'button:contains("Submit")'
                ];
                
                for (const selector of submitSelectors) {
                    try {
                        if (selector.includes(':contains(')) {
                            const text = selector.match(/contains\("([^"]+)"\)/)[1];
                            const xpath = `//button[contains(text(), "${text}")]`;
                            const elements = await this.page.$x(xpath);
                            if (elements.length > 0) {
                                await elements[0].click();
                                logger.info(`Clicked submit button: ${text}`);
                                break;
                            }
                        } else {
                            const element = await this.page.$(selector);
                            if (element) {
                                await element.click();
                                logger.info(`Clicked submit button: ${selector}`);
                                break;
                            }
                        }
                    } catch (error) {
                        logger.debug(`Submit selector ${selector} not found`);
                    }
                }
            }
            
            // Wait for redirect after captcha
            await sleep(5000);
            
            // Look for GoFile links or download links
            return await this.extractFinalLink();
            
        } catch (error) {
            logger.error(`Failed to handle FileCrypt page: ${error.message}`);
            throw error;
        }
    }

    /**
     * Handle direct GoFile page
     */
    async handleGoFilePage() {
        try {
            logger.info('Handling direct GoFile page...');
            
            // Wait for GoFile page to load
            await sleep(5000);
            
            // Look for download button or link
            const downloadSelectors = [
                '.download-button',
                '#download-button',
                'a[href*="download"]',
                '.btn-download',
                'button:contains("Download")',
                'a:contains("Download")'
            ];
            
            for (const selector of downloadSelectors) {
                try {
                    if (selector.includes(':contains(')) {
                        const text = selector.match(/contains\("([^"]+)"\)/)[1];
                        const xpath = `//button[contains(text(), "${text}")] | //a[contains(text(), "${text}")]`;
                        const elements = await this.page.$x(xpath);
                        if (elements.length > 0) {
                            const href = await this.page.evaluate(el => el.href || el.onclick, elements[0]);
                            if (href) {
                                logger.info(`Found GoFile download link: ${href}`);
                                return href;
                            }
                        }
                    } else {
                        const element = await this.page.$(selector);
                        if (element) {
                            const href = await this.page.evaluate(el => el.href, element);
                            if (href) {
                                logger.info(`Found GoFile download link: ${href}`);
                                return href;
                            }
                        }
                    }
                } catch (error) {
                    logger.debug(`Download selector ${selector} not found`);
                }
            }
            
            // Return current URL if no specific download link found
            const currentUrl = this.scraper.getCurrentUrl();
            logger.info(`GoFile final URL: ${currentUrl}`);
            return currentUrl;
            
        } catch (error) {
            logger.error(`Failed to handle GoFile page: ${error.message}`);
            throw error;
        }
    }

    /**
     * Extract final download link from current page
     */
    async extractFinalLink() {
        try {
            // Look for various types of download links
            const linkSelectors = [
                'a[href*="gofile.io"]',
                'a[href*="download"]',
                'a[href*=".zip"]',
                'a[href*=".rar"]',
                'a[href*=".7z"]',
                '.download-link a',
                '#download-link'
            ];
            
            for (const selector of linkSelectors) {
                try {
                    const elements = await this.page.$$(selector);
                    for (const element of elements) {
                        const href = await this.page.evaluate(el => el.href, element);
                        if (href && href.includes('http')) {
                            logger.info(`Found final download link: ${href}`);
                            return href;
                        }
                    }
                } catch (error) {
                    logger.debug(`Link selector ${selector} not found`);
                }
            }
            
            // Return current URL as fallback
            const currentUrl = this.scraper.getCurrentUrl();
            logger.info(`Final URL: ${currentUrl}`);
            return currentUrl;
            
        } catch (error) {
            logger.error(`Failed to extract final link: ${error.message}`);
            return this.scraper.getCurrentUrl();
        }
    }
}

export default GoFileHandler;
