import logger from '../logger.js';

/**
 * Sleep for specified milliseconds
 * @param {number} ms - Milliseconds to sleep
 */
export const sleep = (ms) => new Promise(resolve => setTimeout(resolve, ms));

/**
 * Retry a function with exponential backoff
 * @param {Function} fn - Function to retry
 * @param {number} maxRetries - Maximum number of retries
 * @param {number} baseDelay - Base delay in milliseconds
 * @param {string} operation - Operation name for logging
 */
export const retryWithBackoff = async (fn, maxRetries = 3, baseDelay = 1000, operation = 'operation') => {
    let lastError;
    
    for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
            logger.info(`Attempting ${operation} (attempt ${attempt}/${maxRetries})`);
            return await fn();
        } catch (error) {
            lastError = error;
            logger.warn(`${operation} failed on attempt ${attempt}: ${error.message}`);
            
            if (attempt < maxRetries) {
                const delay = baseDelay * Math.pow(2, attempt - 1);
                logger.info(`Retrying ${operation} in ${delay}ms...`);
                await sleep(delay);
            }
        }
    }
    
    logger.error(`${operation} failed after ${maxRetries} attempts`);
    throw lastError;
};

/**
 * Sanitize filename for safe file system usage
 * @param {string} filename - Original filename
 * @returns {string} - Sanitized filename
 */
export const sanitizeFilename = (filename) => {
    return filename
        .replace(/[<>:"/\\|?*]/g, '_')
        .replace(/\s+/g, '_')
        .toLowerCase();
};

/**
 * Extract domain from URL
 * @param {string} url - URL to extract domain from
 * @returns {string} - Domain name
 */
export const extractDomain = (url) => {
    try {
        return new URL(url).hostname;
    } catch (error) {
        logger.warn(`Failed to extract domain from URL: ${url}`);
        return 'unknown';
    }
};

/**
 * Check if URL is valid
 * @param {string} url - URL to validate
 * @returns {boolean} - True if valid URL
 */
export const isValidUrl = (url) => {
    try {
        new URL(url);
        return true;
    } catch {
        return false;
    }
};

/**
 * Format bytes to human readable string
 * @param {number} bytes - Number of bytes
 * @returns {string} - Formatted string
 */
export const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

/**
 * Generate timestamp string
 * @returns {string} - Formatted timestamp
 */
export const getTimestamp = () => {
    return new Date().toISOString().replace(/[:.]/g, '-');
};
