import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import dotenv from 'dotenv';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables
dotenv.config();

class Config {
    constructor() {
        this.loadConfig();
    }

    loadConfig() {
        try {
            // Load games configuration
            const configPath = path.join(path.dirname(path.dirname(__dirname)), 'config', 'games.json');
            const configData = fs.readFileSync(configPath, 'utf8');
            const config = JSON.parse(configData);
            
            this.games = config.games;
            this.settings = config.settings;
            
            // Override with environment variables if present
            this.settings.browser.headless = process.env.HEADLESS === 'true';
            this.settings.browser.viewport.width = parseInt(process.env.VIEWPORT_WIDTH) || this.settings.browser.viewport.width;
            this.settings.browser.viewport.height = parseInt(process.env.VIEWPORT_HEIGHT) || this.settings.browser.viewport.height;
            
            this.settings.delays.pageLoad = parseInt(process.env.PAGE_LOAD_DELAY) || this.settings.delays.pageLoad;
            this.settings.delays.betweenRequests = parseInt(process.env.BETWEEN_REQUESTS_DELAY) || this.settings.delays.betweenRequests;
            this.settings.delays.captchaSolve = parseInt(process.env.CAPTCHA_SOLVE_DELAY) || this.settings.delays.captchaSolve;
            this.settings.delays.downloadWait = parseInt(process.env.DOWNLOAD_WAIT_DELAY) || this.settings.delays.downloadWait;
            
            this.settings.retries.maxRetries = parseInt(process.env.MAX_RETRIES) || this.settings.retries.maxRetries;
            this.settings.retries.retryDelay = parseInt(process.env.RETRY_DELAY) || this.settings.retries.retryDelay;
            
            if (process.env.USER_AGENT) {
                this.settings.browser.userAgent = process.env.USER_AGENT;
            }
            
        } catch (error) {
            throw new Error(`Failed to load configuration: ${error.message}`);
        }
    }

    getGames() {
        return this.games;
    }

    getSettings() {
        return this.settings;
    }

    updateGames(newGames) {
        this.games = newGames;
    }

    getBrowserConfig() {
        return {
            headless: this.settings.browser.headless,
            viewport: this.settings.browser.viewport,
            userAgent: this.settings.browser.userAgent
        };
    }

    getDelays() {
        return this.settings.delays;
    }

    getRetryConfig() {
        return this.settings.retries;
    }

    getBaseUrl() {
        return this.settings.baseUrl;
    }

    getSearchUrlTemplate() {
        return this.settings.searchUrlTemplate;
    }
}

export default new Config();
