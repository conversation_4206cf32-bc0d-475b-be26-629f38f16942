#!/usr/bin/env node

import { Command } from 'commander';
import chalk from 'chalk';
import ora from 'ora';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import SteamripScraper from './scraper.js';
import logger from './logger.js';
import config from './utils/config.js';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const program = new Command();

// CLI Configuration
program
    .name('steamrip-scraper')
    .description('Automated web scraping tool for steamrip.com')
    .version('1.0.0');

program
    .option('-g, --games <games>', 'Comma-separated list of games to search for')
    .option('-o, --output <file>', 'Output file for results', 'results.json')
    .option('--headless', 'Run browser in headless mode')
    .option('--no-headless', 'Run browser with GUI (default)')
    .option('-v, --verbose', 'Enable verbose logging')
    .option('--config <file>', 'Custom configuration file path')
    .parse();

const options = program.opts();

/**
 * Display banner
 */
function displayBanner() {
    console.log(chalk.cyan(`
╔═══════════════════════════════════════════════════════════════╗
║                    SteamRip Scraper v1.0.0                   ║
║                                                               ║
║  Automated web scraping tool for steamrip.com                ║
║  ⚠️  For educational purposes only                            ║
╚═══════════════════════════════════════════════════════════════╝
    `));
}

/**
 * Display disclaimer
 */
function displayDisclaimer() {
    console.log(chalk.yellow(`
⚠️  IMPORTANT DISCLAIMER:
This tool is intended for educational purposes only.
Users must comply with steamrip.com's terms of service and applicable laws.
Use responsibly and ethically.
    `));
}

/**
 * Parse games from command line or config
 */
function parseGames() {
    if (options.games) {
        return options.games.split(',').map(game => game.trim());
    }
    return config.getGames();
}

/**
 * Setup configuration overrides
 */
function setupConfig() {
    // Override headless mode if specified
    if (options.headless !== undefined) {
        config.settings.browser.headless = options.headless;
    }
    
    // Set verbose logging
    if (options.verbose) {
        process.env.LOG_LEVEL = 'debug';
    }
    
    // Load custom config if specified
    if (options.config) {
        try {
            const customConfigPath = path.resolve(options.config);
            const customConfig = JSON.parse(fs.readFileSync(customConfigPath, 'utf8'));
            
            // Merge custom config with default config
            Object.assign(config.settings, customConfig.settings || {});
            if (customConfig.games) {
                config.games = customConfig.games;
            }
            
            logger.info(`Loaded custom configuration from: ${customConfigPath}`);
        } catch (error) {
            logger.error(`Failed to load custom config: ${error.message}`);
            process.exit(1);
        }
    }
}

/**
 * Display progress and statistics
 */
function displayProgress(scraper, gameIndex, totalGames, gameName) {
    const summary = scraper.getSummary();
    
    console.log(chalk.blue(`\n📊 Progress: ${gameIndex}/${totalGames} games processed`));
    console.log(chalk.green(`✅ Successful: ${summary.successfulGames}`));
    console.log(chalk.red(`❌ Failed: ${summary.failedGames}`));
    console.log(chalk.cyan(`🔗 Total links found: ${summary.totalLinks}`));
    console.log(chalk.magenta(`📈 Success rate: ${summary.successRate}`));
    
    if (gameName) {
        console.log(chalk.white(`🎮 Currently processing: ${gameName}`));
    }
}

/**
 * Main execution function
 */
async function main() {
    displayBanner();
    displayDisclaimer();
    
    // Setup configuration
    setupConfig();
    
    // Parse games list
    const games = parseGames();
    
    console.log(chalk.blue(`\n🎮 Games to process: ${games.length}`));
    games.forEach((game, index) => {
        console.log(chalk.white(`  ${index + 1}. ${game}`));
    });
    
    console.log(chalk.blue(`\n📁 Output file: ${options.output}`));
    console.log(chalk.blue(`🖥️  Headless mode: ${config.settings.browser.headless ? 'Yes' : 'No'}`));
    
    const scraper = new SteamripScraper();
    let spinner;
    
    try {
        // Initialize browser
        spinner = ora('Initializing browser...').start();
        await scraper.initialize();
        spinner.succeed('Browser initialized');
        
        // Start scraping
        console.log(chalk.green('\n🚀 Starting scraping process...\n'));
        
        const results = [];
        
        for (let i = 0; i < games.length; i++) {
            const gameName = games[i];
            
            spinner = ora(`Processing ${gameName} (${i + 1}/${games.length})`).start();
            
            try {
                // Process single game
                const gameResult = await scraper.processGame(gameName);
                
                if (gameResult.success) {
                    spinner.text = `Extracting download links for ${gameName}...`;
                    
                    // Extract and process download links
                    const downloadLinks = await scraper.extractDownloadLinks();
                    const processedLinks = await scraper.processDownloadLinks(downloadLinks);
                    
                    // Handle anti-bot measures
                    await scraper.handlePopupsAndRedirects();
                    
                    const result = {
                        ...gameResult,
                        downloadLinks: downloadLinks,
                        processedLinks: processedLinks,
                        timestamp: new Date().toISOString()
                    };
                    
                    results.push(result);
                    scraper.results.push(result);
                    
                    spinner.succeed(`✅ ${gameName}: ${processedLinks.length} links found`);
                } else {
                    results.push(gameResult);
                    spinner.fail(`❌ ${gameName}: ${gameResult.error}`);
                }
                
            } catch (error) {
                const errorResult = {
                    gameName,
                    success: false,
                    error: error.message,
                    timestamp: new Date().toISOString()
                };
                results.push(errorResult);
                spinner.fail(`❌ ${gameName}: ${error.message}`);
            }
            
            // Display progress
            displayProgress(scraper, i + 1, games.length);
            
            // Add delay between games
            if (i < games.length - 1) {
                await new Promise(resolve => setTimeout(resolve, config.getDelays().betweenRequests));
            }
        }
        
        // Save results
        spinner = ora('Saving results...').start();
        const outputPath = await scraper.saveResults(options.output);
        spinner.succeed(`Results saved to: ${outputPath}`);
        
        // Display final summary
        const finalSummary = scraper.getSummary();
        console.log(chalk.green('\n🎉 Scraping completed!'));
        console.log(chalk.blue('\n📊 Final Summary:'));
        console.log(chalk.white(`  Total games processed: ${finalSummary.totalGames}`));
        console.log(chalk.green(`  Successful: ${finalSummary.successfulGames}`));
        console.log(chalk.red(`  Failed: ${finalSummary.failedGames}`));
        console.log(chalk.cyan(`  Total download links: ${finalSummary.totalLinks}`));
        console.log(chalk.magenta(`  Success rate: ${finalSummary.successRate}`));
        
    } catch (error) {
        if (spinner) {
            spinner.fail(`Fatal error: ${error.message}`);
        }
        logger.error(`Fatal error: ${error.message}`);
        console.error(chalk.red(`\n💥 Fatal error: ${error.message}`));
        process.exit(1);
    } finally {
        // Cleanup
        try {
            await scraper.cleanup();
            console.log(chalk.blue('\n🧹 Cleanup completed'));
        } catch (error) {
            logger.error(`Cleanup error: ${error.message}`);
        }
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log(chalk.yellow('\n\n⚠️  Process interrupted by user'));
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log(chalk.yellow('\n\n⚠️  Process terminated'));
    process.exit(0);
});

// Run the main function
main().catch(error => {
    logger.error(`Unhandled error: ${error.message}`);
    console.error(chalk.red(`\n💥 Unhandled error: ${error.message}`));
    process.exit(1);
});
