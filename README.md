# SteamRip Scraper

An automated web scraping tool for steamrip.com that extracts download links for games.

## ⚠️ Important Disclaimer

This tool is intended for **educational purposes only**. Users must:
- Comply with steamrip.com's terms of service
- Respect applicable laws regarding web scraping and copyright
- Use responsibly and ethically
- Not use for commercial purposes

## Features

- 🎮 Automated game search on steamrip.com
- 🔗 Download link extraction from multiple providers
- 🤖 Anti-bot measures (captcha detection, popup handling)
- 📝 Configurable game list without code changes
- 🛡️ Robust error handling and retry logic
- 📊 Comprehensive logging and progress tracking

## Supported Download Providers

- **DataNodes** (Priority handling)
- **GoFile** (with FileCrypt captcha support)
- **BuzzHeavier**
- **MegaUp**
- Other common providers

## Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd steamrip-scraper
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment configuration:
```bash
cp .env.example .env
```

4. Configure your game list in `config/games.json`

## Usage

### Basic Usage
```bash
npm start
```

### Command Line Options
```bash
# Run with specific games
npm start -- --games "Elden Ring,Cyberpunk 2077"

# Run in headless mode
npm start -- --headless

# Custom output file
npm start -- --output results.json
```

## Configuration

### Game List (`config/games.json`)
Edit the games array to add or remove games to search for.

### Environment Variables (`.env`)
Customize delays, retry settings, and browser configuration.

## Output

The tool generates:
- `results.json` - Extracted download links
- `logs/scraper.log` - Detailed operation logs
- Console output with progress indicators

## Project Structure

```
steamrip-scraper/
├── src/
│   ├── index.js              # Main entry point
│   ├── scraper.js            # Core scraper class
│   ├── providers/            # Download provider handlers
│   ├── utils/                # Utility functions
│   └── logger.js             # Logging configuration
├── config/
│   └── games.json            # Game configuration
├── test/
│   └── test.js               # Test files
├── logs/                     # Log files
└── downloads/                # Downloaded content
```

## Development

### Running Tests
```bash
npm test
```

### Debugging
```bash
npm run dev
```

## Legal Notice

This software is provided "as is" without warranty. Users are responsible for ensuring their use complies with all applicable laws and website terms of service.
