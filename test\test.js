import SteamripScraper from '../src/scraper.js';
import logger from '../src/logger.js';
import config from '../src/utils/config.js';
import { sleep } from '../src/utils/helpers.js';

/**
 * Simple test runner for the SteamRip Scraper
 */
class TestRunner {
    constructor() {
        this.tests = [];
        this.passed = 0;
        this.failed = 0;
    }

    /**
     * Add a test case
     */
    test(name, testFn) {
        this.tests.push({ name, testFn });
    }

    /**
     * Run all tests
     */
    async run() {
        console.log('🧪 Running SteamRip Scraper Tests\n');
        
        for (const test of this.tests) {
            try {
                console.log(`⏳ Running: ${test.name}`);
                await test.testFn();
                console.log(`✅ PASSED: ${test.name}\n`);
                this.passed++;
            } catch (error) {
                console.log(`❌ FAILED: ${test.name}`);
                console.log(`   Error: ${error.message}\n`);
                this.failed++;
            }
        }
        
        console.log(`📊 Test Results:`);
        console.log(`   Passed: ${this.passed}`);
        console.log(`   Failed: ${this.failed}`);
        console.log(`   Total: ${this.tests.length}`);
        
        if (this.failed > 0) {
            process.exit(1);
        }
    }

    /**
     * Assert helper
     */
    assert(condition, message) {
        if (!condition) {
            throw new Error(message || 'Assertion failed');
        }
    }
}

const runner = new TestRunner();

// Test configuration loading
runner.test('Configuration Loading', async () => {
    const games = config.getGames();
    runner.assert(Array.isArray(games), 'Games should be an array');
    runner.assert(games.length > 0, 'Games array should not be empty');
    
    const settings = config.getSettings();
    runner.assert(typeof settings === 'object', 'Settings should be an object');
    runner.assert(settings.baseUrl, 'Base URL should be defined');
    runner.assert(settings.searchUrlTemplate, 'Search URL template should be defined');
});

// Test scraper initialization
runner.test('Scraper Initialization', async () => {
    const scraper = new SteamripScraper();
    
    runner.assert(scraper.browser === null, 'Browser should be null initially');
    runner.assert(scraper.page === null, 'Page should be null initially');
    runner.assert(Array.isArray(scraper.results), 'Results should be an array');
    
    // Test initialization
    await scraper.initialize();
    
    runner.assert(scraper.browser !== null, 'Browser should be initialized');
    runner.assert(scraper.page !== null, 'Page should be initialized');
    runner.assert(scraper.dataNodesHandler !== null, 'DataNodes handler should be initialized');
    runner.assert(scraper.goFileHandler !== null, 'GoFile handler should be initialized');
    runner.assert(scraper.genericHandler !== null, 'Generic handler should be initialized');
    
    // Cleanup
    await scraper.cleanup();
});

// Test URL validation
runner.test('URL Validation', async () => {
    const { isValidUrl } = await import('../src/utils/helpers.js');
    
    runner.assert(isValidUrl('https://steamrip.com'), 'Valid HTTPS URL should pass');
    runner.assert(isValidUrl('http://example.com'), 'Valid HTTP URL should pass');
    runner.assert(!isValidUrl('invalid-url'), 'Invalid URL should fail');
    runner.assert(!isValidUrl(''), 'Empty string should fail');
});

// Test provider identification
runner.test('Provider Identification', async () => {
    const scraper = new SteamripScraper();
    
    runner.assert(scraper.identifyProvider('https://gofile.io/test') === 'GoFile', 'Should identify GoFile');
    runner.assert(scraper.identifyProvider('https://buzzheavier.com/test') === 'BuzzHeavier', 'Should identify BuzzHeavier');
    runner.assert(scraper.identifyProvider('https://megaup.net/test') === 'MegaUp', 'Should identify MegaUp');
    runner.assert(scraper.identifyProvider('https://datanodes.to/test') === 'DataNodes', 'Should identify DataNodes');
    runner.assert(scraper.identifyProvider('https://unknown.com/test') === 'Unknown', 'Should identify unknown provider');
});

// Test search URL generation
runner.test('Search URL Generation', async () => {
    const searchTemplate = config.getSearchUrlTemplate();
    const testGame = 'Elden Ring';
    const expectedUrl = searchTemplate.replace('{search_query}', encodeURIComponent(testGame));
    
    runner.assert(expectedUrl.includes('steamrip.com'), 'URL should contain steamrip.com');
    runner.assert(expectedUrl.includes('Elden%20Ring'), 'URL should contain encoded game name');
});

// Test helper functions
runner.test('Helper Functions', async () => {
    const { sanitizeFilename, extractDomain, formatBytes } = await import('../src/utils/helpers.js');
    
    // Test filename sanitization
    const sanitized = sanitizeFilename('Test Game: Special Edition!');
    runner.assert(!sanitized.includes(':'), 'Sanitized filename should not contain colons');
    runner.assert(!sanitized.includes('!'), 'Sanitized filename should not contain exclamation marks');
    
    // Test domain extraction
    const domain = extractDomain('https://example.com/path/to/file');
    runner.assert(domain === 'example.com', 'Should extract correct domain');
    
    // Test byte formatting
    const formatted = formatBytes(1024);
    runner.assert(formatted === '1 KB', 'Should format bytes correctly');
});

// Test error handling
runner.test('Error Handling', async () => {
    const { retryWithBackoff } = await import('../src/utils/helpers.js');
    
    let attempts = 0;
    const failingFunction = async () => {
        attempts++;
        if (attempts < 3) {
            throw new Error('Test error');
        }
        return 'success';
    };
    
    const result = await retryWithBackoff(failingFunction, 3, 100, 'test operation');
    runner.assert(result === 'success', 'Should succeed after retries');
    runner.assert(attempts === 3, 'Should make correct number of attempts');
});

// Test configuration overrides
runner.test('Configuration Overrides', async () => {
    const originalHeadless = config.settings.browser.headless;
    
    // Test headless override
    config.settings.browser.headless = true;
    const browserConfig = config.getBrowserConfig();
    runner.assert(browserConfig.headless === true, 'Should override headless setting');
    
    // Restore original setting
    config.settings.browser.headless = originalHeadless;
});

// Integration test (optional - requires network access)
runner.test('Basic Navigation Test', async () => {
    // Skip this test if running in CI or without network
    if (process.env.SKIP_INTEGRATION_TESTS) {
        console.log('   Skipping integration test');
        return;
    }
    
    const scraper = new SteamripScraper();
    
    try {
        await scraper.initialize();
        
        // Test navigation to a simple page
        await scraper.navigateToUrl('https://httpbin.org/html');
        
        const currentUrl = scraper.getCurrentUrl();
        runner.assert(currentUrl.includes('httpbin.org'), 'Should navigate to test URL');
        
        // Test element waiting
        const element = await scraper.waitForElement('body');
        runner.assert(element !== null, 'Should find body element');
        
    } finally {
        await scraper.cleanup();
    }
});

// Run all tests
runner.run().catch(error => {
    console.error('Test runner error:', error);
    process.exit(1);
});
